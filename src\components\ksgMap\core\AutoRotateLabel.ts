import { KsgLabel } from "./KsgLabel";
import type { Point } from "../types";
import { createClosestPointDetector, type ClosestPointResult } from "../utils/closestPointDetector";
import type { PerspectiveCamera } from "three";

/**
 * 自动旋转标签管理器
 * 
 * 负责在自动旋转过程中管理最靠近屏幕中央的点的标签显示
 * 当点靠近屏幕中心时显示标签，离开时隐藏标签
 */
export class AutoRotateLabel {
  /** 标签实例 */
  private label: KsgLabel;
  
  /** 最靠近点检测器 */
  private closestPointDetector: ReturnType<typeof createClosestPointDetector>;
  
  /** 当前显示标签的点 */
  private currentPoint: Point | null = null;
  
  /** 是否启用自动标签显示 */
  private enabled = false;
  
  /** 更新频率控制（避免过于频繁的检测） */
  private updateCounter = 0;
  private updateInterval = 5; // 每5帧检测一次
  
  /** 标签显示延迟定时器 */
  private showTimer: ReturnType<typeof setTimeout> | null = null;
  private hideTimer: ReturnType<typeof setTimeout> | null = null;
  
  /** 配置选项 */
  private config = {
    showDelay: 500,    // 显示延迟（毫秒）
    hideDelay: 200,    // 隐藏延迟（毫秒）
    updateInterval: 5, // 更新间隔（帧数）
  };

  /**
   * 构造函数
   * 
   * @param camera 透视相机对象
   * @param rendererWidth 渲染器宽度
   * @param rendererHeight 渲染器高度
   */
  constructor(
    camera: PerspectiveCamera,
    rendererWidth: number,
    rendererHeight: number
  ) {
    // 创建专用的标签实例
    this.label = new KsgLabel();
    this.label.element.classList.add('auto-rotate-label');
    
    // 创建最靠近点检测器
    this.closestPointDetector = createClosestPointDetector(
      camera,
      rendererWidth,
      rendererHeight,
      {
        centerRadius: Math.min(rendererWidth, rendererHeight) * 0.25, // 屏幕较小边的25%
        minDistanceThreshold: 80, // 80像素
        visibleOnly: true
      }
    );
  }

  /**
   * 启用自动标签显示
   */
  enable() {
    this.enabled = true;
  }

  /**
   * 禁用自动标签显示并隐藏当前标签
   */
  disable() {
    this.enabled = false;
    this.hideCurrentLabel();
  }

  /**
   * 更新检测和标签显示
   * 在渲染循环中调用
   * 
   * @param points 所有可检测的点数组
   */
  update(points: Point[]) {
    if (!this.enabled) return;

    // 控制更新频率
    this.updateCounter++;
    if (this.updateCounter < this.config.updateInterval) {
      return;
    }
    this.updateCounter = 0;

    // 检测最靠近的点
    const result = this.closestPointDetector.detectClosestPoint(points);
    
    // 处理检测结果
    this.handleDetectionResult(result);
  }

  /**
   * 处理检测结果
   * 
   * @param result 检测结果
   */
  private handleDetectionResult(result: ClosestPointResult) {
    const newPoint = result.point;
    
    // 如果检测到的点与当前显示的点相同，无需处理
    if (newPoint === this.currentPoint) {
      return;
    }

    // 如果当前有显示的点，但新检测的点不同，需要隐藏当前标签
    if (this.currentPoint && newPoint !== this.currentPoint) {
      this.scheduleHideLabel();
    }

    // 如果检测到新的点，准备显示新标签
    if (newPoint && newPoint !== this.currentPoint) {
      this.scheduleShowLabel(newPoint);
    }

    // 如果没有检测到点，隐藏标签
    if (!newPoint && this.currentPoint) {
      this.scheduleHideLabel();
    }
  }

  /**
   * 计划显示标签
   * 
   * @param point 要显示标签的点
   */
  private scheduleShowLabel(point: Point) {
    // 清除之前的定时器
    this.clearTimers();
    
    // 设置显示延迟
    this.showTimer = setTimeout(() => {
      this.showLabel(point);
      this.showTimer = null;
    }, this.config.showDelay);
  }

  /**
   * 计划隐藏标签
   */
  private scheduleHideLabel() {
    // 清除之前的定时器
    this.clearTimers();
    
    // 设置隐藏延迟
    this.hideTimer = setTimeout(() => {
      this.hideCurrentLabel();
      this.hideTimer = null;
    }, this.config.hideDelay);
  }

  /**
   * 显示标签
   * 
   * @param point 要显示标签的点
   */
  private showLabel(point: Point) {
    // 隐藏当前标签（如果有）
    if (this.currentPoint) {
      this.label.hide();
    }

    // 显示新标签
    this.label.display(point);
    this.currentPoint = point;
  }

  /**
   * 隐藏当前标签
   */
  private hideCurrentLabel() {
    if (this.currentPoint) {
      this.label.hide();
      this.currentPoint = null;
    }
  }

  /**
   * 清除所有定时器
   */
  private clearTimers() {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
  }

  /**
   * 更新渲染器尺寸
   * 
   * @param width 新的宽度
   * @param height 新的高度
   */
  updateSize(width: number, height: number) {
    this.closestPointDetector.updateSize(width, height);
  }

  /**
   * 更新配置
   * 
   * @param newConfig 新的配置选项
   */
  updateConfig(newConfig: Partial<typeof this.config>) {
    Object.assign(this.config, newConfig);
  }

  /**
   * 获取标签实例（用于添加到场景）
   */
  getLabel() {
    return this.label;
  }

  /**
   * 获取当前显示的点
   */
  getCurrentPoint() {
    return this.currentPoint;
  }

  /**
   * 检查是否启用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 立即隐藏标签（不使用延迟）
   */
  immediateHide() {
    this.clearTimers();
    this.hideCurrentLabel();
  }

  /**
   * 立即显示指定点的标签（不使用延迟）
   * 
   * @param point 要显示标签的点
   */
  immediateShow(point: Point) {
    this.clearTimers();
    this.showLabel(point);
  }

  /**
   * 清理资源
   */
  dispose() {
    this.clearTimers();
    this.hideCurrentLabel();
    this.enabled = false;
  }
}

// 创建全局实例（延迟初始化）
let autoRotateLabel: AutoRotateLabel | null = null;

/**
 * 获取自动旋转标签管理器实例
 * 
 * @param camera 透视相机对象
 * @param rendererWidth 渲染器宽度
 * @param rendererHeight 渲染器高度
 * @returns 自动旋转标签管理器实例
 */
export function getAutoRotateLabel(
  camera: PerspectiveCamera,
  rendererWidth: number,
  rendererHeight: number
): AutoRotateLabel {
  if (!autoRotateLabel) {
    autoRotateLabel = new AutoRotateLabel(camera, rendererWidth, rendererHeight);
  }
  return autoRotateLabel;
}

/**
 * 重置自动旋转标签管理器实例
 * 用于清理或重新初始化
 */
export function resetAutoRotateLabel() {
  if (autoRotateLabel) {
    autoRotateLabel.dispose();
    autoRotateLabel = null;
  }
}

export default AutoRotateLabel;
