import { PerspectiveCamera } from "three";
import type { Point } from "../types";
import { getPositionOnScreen, isPointVisible } from "./index";

/**
 * 最靠近屏幕中央的点检测器
 * 
 * 该模块负责在自动旋转过程中检测哪个知识点最靠近屏幕中央
 * 主要用于自动旋转时显示最相关的点名称标签
 */

/**
 * 检测结果接口
 */
export interface ClosestPointResult {
  /** 最靠近的点对象，如果没有可见点则为null */
  point: Point | null;
  /** 到屏幕中心的距离（像素），如果没有可见点则为Infinity */
  distance: number;
  /** 屏幕坐标位置 */
  screenPosition: { x: number; y: number } | null;
}

/**
 * 检测配置选项
 */
export interface DetectorOptions {
  /** 屏幕中心区域半径（像素），只有在此范围内的点才被考虑 */
  centerRadius?: number;
  /** 最小距离阈值（像素），距离小于此值的点才被认为是"靠近中心" */
  minDistanceThreshold?: number;
  /** 是否只检测可见的点 */
  visibleOnly?: boolean;
}

/**
 * 创建最靠近屏幕中央的点检测器
 * 
 * @param camera 透视相机对象
 * @param rendererWidth 渲染器宽度
 * @param rendererHeight 渲染器高度
 * @param options 检测配置选项
 * @returns 检测器对象
 */
export function createClosestPointDetector(
  camera: PerspectiveCamera,
  rendererWidth: number,
  rendererHeight: number,
  options: DetectorOptions = {}
) {
  // 默认配置
  const config = {
    centerRadius: Math.min(rendererWidth, rendererHeight) * 0.3, // 屏幕较小边的30%
    minDistanceThreshold: 100, // 100像素
    visibleOnly: true,
    ...options
  };

  // 屏幕中心坐标
  const screenCenter = {
    x: rendererWidth / 2,
    y: rendererHeight / 2
  };

  /**
   * 计算点到屏幕中心的距离
   * @param screenPos 屏幕坐标位置
   * @returns 距离（像素）
   */
  function calculateDistanceToCenter(screenPos: { x: number; y: number }): number {
    const dx = screenPos.x - screenCenter.x;
    const dy = screenPos.y - screenCenter.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 检测最靠近屏幕中央的点
   * 
   * @param points 要检测的点数组
   * @returns 检测结果
   */
  function detectClosestPoint(points: Point[]): ClosestPointResult {
    let closestPoint: Point | null = null;
    let minDistance = Infinity;
    let closestScreenPosition: { x: number; y: number } | null = null;

    for (const point of points) {
      // 如果配置要求只检测可见点，先检查可见性
      if (config.visibleOnly && !isPointVisible(point.coordinate, camera)) {
        continue;
      }

      // 计算点在屏幕上的位置
      const screenPos = getPositionOnScreen(
        point.coordinate,
        camera,
        rendererWidth,
        rendererHeight
      );

      // 计算到屏幕中心的距离
      const distance = calculateDistanceToCenter(screenPos);

      // 检查是否在中心区域内
      if (distance > config.centerRadius) {
        continue;
      }

      // 更新最近的点
      if (distance < minDistance) {
        minDistance = distance;
        closestPoint = point;
        closestScreenPosition = screenPos;
      }
    }

    // 检查是否满足最小距离阈值
    if (minDistance > config.minDistanceThreshold) {
      return {
        point: null,
        distance: Infinity,
        screenPosition: null
      };
    }

    return {
      point: closestPoint,
      distance: minDistance,
      screenPosition: closestScreenPosition
    };
  }

  /**
   * 更新渲染器尺寸
   * 当窗口大小改变时调用
   * 
   * @param width 新的宽度
   * @param height 新的高度
   */
  function updateSize(width: number, height: number) {
    rendererWidth = width;
    rendererHeight = height;
    
    // 更新屏幕中心坐标
    screenCenter.x = width / 2;
    screenCenter.y = height / 2;
    
    // 更新中心区域半径
    config.centerRadius = Math.min(width, height) * 0.3;
  }

  /**
   * 更新配置选项
   * 
   * @param newOptions 新的配置选项
   */
  function updateOptions(newOptions: Partial<DetectorOptions>) {
    Object.assign(config, newOptions);
  }

  /**
   * 获取当前配置
   * 
   * @returns 当前配置对象的副本
   */
  function getConfig() {
    return { ...config };
  }

  /**
   * 获取屏幕中心坐标
   * 
   * @returns 屏幕中心坐标
   */
  function getScreenCenter() {
    return { ...screenCenter };
  }

  // 返回检测器接口
  return {
    detectClosestPoint,
    updateSize,
    updateOptions,
    getConfig,
    getScreenCenter,
    calculateDistanceToCenter
  };
}

/**
 * 创建简化版的最靠近点检测器
 * 使用默认配置，适用于大多数场景
 * 
 * @param camera 透视相机对象
 * @param rendererWidth 渲染器宽度
 * @param rendererHeight 渲染器高度
 * @returns 简化的检测函数
 */
export function createSimpleClosestPointDetector(
  camera: PerspectiveCamera,
  rendererWidth: number,
  rendererHeight: number
) {
  const detector = createClosestPointDetector(camera, rendererWidth, rendererHeight);
  
  return {
    detect: detector.detectClosestPoint,
    updateSize: detector.updateSize
  };
}
