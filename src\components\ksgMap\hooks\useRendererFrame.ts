/**
 * 渲染帧管理器 - KsgMap 3D 场景渲染循环核心模块
 *
 * 该模块负责管理 Three.js 场景的渲染循环，协调各个渲染组件的更新
 * 是 KsgMap 可视化系统的渲染引擎，确保场景的流畅显示和交互响应
 */

import ctx from "../ctx";
import TWEEN from "@tweenjs/tween.js";
import { Clock, Vector2 } from "three";
import KsgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { getAutoRotateLabel } from "../core/AutoRotateLabel";
import { VIEW_MODE } from "../enums";

/**
 * 渲染帧 Hook - 管理 3D 场景的渲染循环和动画更新
 *
 * 功能特性：
 * - 统一管理场景渲染循环
 * - 协调多个渲染器的更新（WebGL、CSS2D）
 * - 处理动画系统更新（Tween、节点动画、焦点动画）
 * - 管理相机控制器更新
 * - 提供稳定的 60FPS 渲染性能
 *
 * @returns 包含渲染循环启动方法的对象
 */
export default function useRenderFrame() {
  // Three.js 时钟对象 - 用于计算每帧的时间间隔，确保动画的时间一致性
  const clock = new Clock();

  // 自动旋转标签管理器实例（延迟初始化）
  let autoRotateLabel: ReturnType<typeof getAutoRotateLabel> | null = null;

  /**
   * 启动渲染循环 - 核心渲染函数，每帧调用一次
   *
   * 渲染管道执行顺序：
   * 1. 计算帧时间间隔
   * 2. CSS2D 标签渲染（知识点标签）
   * 3. WebGL 主场景渲染
   * 4. 相机控制器更新
   * 5. 动画系统更新
   * 6. 自定义组件更新
   * 7. 递归调用下一帧
   *
   * @param time - 当前时间戳，由 requestAnimationFrame 提供
   */
  function startRenderFrame(time: any = 0) {
    // 计算自上一帧以来的时间间隔（秒）
    const deltaTime = clock.getDelta();

    // 注释：视椎体裁剪更新（可能用于性能优化）
    // updateVisible(ctx.viewGroup!, ctx.camera!);

    // CSS2D 渲染器 - 渲染知识点标签等 2D 元素
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

    // 注释：后处理渲染管道（可能用于特效处理）
    // ctx.composer?.render();

    // 主 WebGL 渲染器 - 渲染 3D 场景
    ctx.renderer?.render(ctx.scene!, ctx.camera!);

    // 相机控制器更新 - 处理用户交互（旋转、缩放、平移）
    ctx.controls?.update(deltaTime);
    // 自动旋转更新（如果启用）
    ctx.controls?.autoRotateUpdate(deltaTime);

    // Tween.js 动画系统更新 - 处理补间动画
    TWEEN.update(time);

    // 递归调用，实现连续渲染循环
    requestAnimationFrame(startRenderFrame);

    // 自定义组件更新
    // 节点悬停动画更新
    KsgHover.update(ctx, deltaTime);
    // 焦点外壳动画更新
    focusCrust.update(deltaTime);
    // 点云网格更新（如果存在）
    if (ctx.pointsMesh) ctx.pointsMesh.update();
    // 焦点连线更新（如果存在）
    if (ctx.focusLine) ctx.focusLine?.update();

    // 自动旋转标签更新（仅在全局视图且自动旋转启用时）
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls?.autoRotate) {
      // 延迟初始化自动旋转标签管理器
      if (!autoRotateLabel && ctx.camera && ctx.renderer) {
        // 获取渲染器的实际尺寸
        const size = new Vector2();
        ctx.renderer.getSize(size);

        autoRotateLabel = getAutoRotateLabel(
          ctx.camera,
          size.x,
          size.y
        );
        // 将标签添加到场景中
        ctx.viewGroup?.add(autoRotateLabel.getLabel());
        autoRotateLabel.enable();
      }

      // 更新自动旋转标签（检测最靠近屏幕中央的点）
      if (autoRotateLabel && ctx.graph?.pointsData) {
        // 将Map转换为数组
        const pointsArray = Array.from(ctx.graph.pointsData.values());
        autoRotateLabel.update(pointsArray);
      }
    } else if (autoRotateLabel) {
      // 如果不在自动旋转状态，禁用标签显示
      autoRotateLabel.disable();
    }
  }

  // 返回渲染控制接口
  return {
    startRenderFrame, // 启动渲染循环的方法
  };
}
